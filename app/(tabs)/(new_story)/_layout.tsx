import { Stack } from 'expo-router';
import { StoryProvider } from '@/contexts/StoryContext';
import { getTranslation } from '@/components/util/LocalizationUtil';
import { Colors } from '@/constants/Colors';

export default function DiscoverLayout() {
  return (
    <StoryProvider>
      <Stack
        screenOptions={{
          headerBackTitleVisible: false,
          headerTintColor: Colors.light.text,
          headerTransparent: true,
          headerTitle: ''
        }}>
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
            title: getTranslation('setupChildProfile')
          }}
        />
        <Stack.Screen
          name="add_character"
          options={{
            title: getTranslation('addOtherCharacters'),
            headerShown: false,
          }}
        />
        <Stack.Screen
          name='story_type'
          options={{
            title: getTranslation('pickStoryType'),
            headerTransparent: true,
          }}
        />
        <Stack.Screen
          name='story_moral'
          options={{
            title: getTranslation('pickStoryMoral'),
            headerTransparent: true,
          }}
        />
        <Stack.Screen
          name='story_theme'
          options={{
            title: getTranslation('pickStoryTheme'),
            headerTransparent: true,
          }}
        />
        <Stack.Screen
          name='story_details'
          options={{
            title: getTranslation('storyDetails'),
            headerTransparent: true,
            headerShown: false,
          }}
        />
      </Stack>
    </StoryProvider>
  );
}